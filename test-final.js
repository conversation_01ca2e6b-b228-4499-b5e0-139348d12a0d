// Final test for Brave Search MCP Server
import fetch from 'node-fetch';

async function testServer() {
    console.log('🧪 Testing Brave Search MCP Server...\n');
    
    const baseUrl = 'http://localhost:8080';
    
    try {
        // Test 1: Health Check
        console.log('1️⃣ Testing Health Check...');
        const healthResponse = await fetch(`${baseUrl}/health`);
        const healthData = await healthResponse.json();
        console.log('✅ Health Check:', healthData);
        
        // Test 2: SSE Endpoint (just check if it responds)
        console.log('\n2️⃣ Testing SSE Endpoint...');
        const sseResponse = await fetch(`${baseUrl}/sse`);
        console.log('✅ SSE Endpoint Status:', sseResponse.status);
        console.log('✅ SSE Content-Type:', sseResponse.headers.get('content-type'));
        
        // Test 3: Metrics Endpoint
        console.log('\n3️⃣ Testing Metrics Endpoint...');
        const metricsResponse = await fetch(`${baseUrl}/metrics`);
        console.log('✅ Metrics Status:', metricsResponse.status);
        
        console.log('\n🎉 All tests passed! Server is ready for Letta AI.');
        console.log('\n📋 Configuration for Letta AI:');
        console.log('   Server Name: Brave Search MCP');
        console.log('   Server URL: http://localhost:8080/sse');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n🔧 Make sure the server is running with: start-for-letta.bat');
    }
}

testServer();
