{{- if not .Values.braveSearch.existingSecret }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "brave-search-mcp-sse.fullname" . }}
  {{- if .Values.namespace }}
  namespace: {{ .Values.namespace }}
  {{- end }}
  labels:
    {{- include "brave-search-mcp-sse.labels" . | nindent 4 }}
type: Opaque
data:
  {{ .Values.braveSearch.existingSecretKey }}: {{ .Values.braveSearch.apiKey | b64enc }}
{{- end }} 