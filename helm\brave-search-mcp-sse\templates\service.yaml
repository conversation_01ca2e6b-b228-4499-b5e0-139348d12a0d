apiVersion: v1
kind: Service
metadata:
  name: {{ include "brave-search-mcp-sse.fullname" . }}
  {{- if .Values.namespace }}
  namespace: {{ .Values.namespace }}
  {{- end }}
  labels:
    {{- include "brave-search-mcp-sse.labels" . | nindent 4 }}
  {{- if not .Values.serviceMonitor.enabled }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics"
    prometheus.io/port: "{{ .Values.service.port }}"
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "brave-search-mcp-sse.selectorLabels" . | nindent 4 }} 