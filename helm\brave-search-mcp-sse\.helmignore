# Patterns to ignore when building packages.
# This supports shell glob matching, relative path matching, and
# negation (prefixed with !). Only one pattern per line.
.DS_Store
*.tgz

# Git files
.git
.gitignore

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Node files
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Build files
dist/
build/

# Test files
__tests__/
*.test.js
*.spec.js

# Docker files
Dockerfile
.dockerignore

# Local development files
.env
.env.local
.env.*.local 