#!/usr/bin/env pwsh

Write-Host "====================================" -ForegroundColor Green
Write-Host "Starting Brave Search MCP SSE Server for Letta AI" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Setze Umgebungsvariablen
$env:BRAVE_API_KEY = "BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ"
$env:PORT = "8081"
$env:LOG_LEVEL = "info"
$env:PUBLIC_URL = "http://localhost:8081"

Write-Host ""
Write-Host "Configuration for Letta AI:" -ForegroundColor Yellow
Write-Host "- Port: $($env:PORT)" -ForegroundColor White
Write-Host "- Log Level: $($env:LOG_LEVEL)" -ForegroundColor White
Write-Host "- API Key: [CONFIGURED]" -ForegroundColor White
Write-Host "- Public URL: $($env:PUBLIC_URL)" -ForegroundColor White
Write-Host ""

# Prüfe ob dist Ordner existiert
if (-not (Test-Path "dist")) {
    Write-Host "Building TypeScript files..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "Starting server for Letta AI..." -ForegroundColor Yellow
Write-Host ""
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "LETTA AI CONFIGURATION:" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Server Name: Brave Search MCP" -ForegroundColor White
Write-Host "Server URL: $($env:PUBLIC_URL)/sse" -ForegroundColor White
Write-Host ""
Write-Host "Copy this URL into Letta AI:" -ForegroundColor Yellow
Write-Host "$($env:PUBLIC_URL)/sse" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Starte den Server
try {
    node dist/index.js
} catch {
    Write-Host ""
    Write-Host "Server stopped with error!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
