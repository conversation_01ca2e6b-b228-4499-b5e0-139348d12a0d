// Test script for Brave Search MCP SSE Server
const EventSource = require('eventsource');
const fetch = require('node-fetch');

async function testBraveSearchSSE() {
    console.log('Testing Brave Search MCP SSE Server...');
    console.log('='.repeat(50));
    
    // 1. Health check
    try {
        const health = await fetch('http://localhost:8080/health');
        const healthData = await health.json();
        console.log('Health Check:', healthData);
    } catch (error) {
        console.error('Health check failed:', error);
        return;
    }
    
    // 2. Establish SSE connection
    console.log('\nEstablishing SSE connection...');
    const eventSource = new EventSource('http://localhost:8080/sse');
    
    eventSource.onopen = () => {
        console.log('SSE connection opened!');
    };
    
    eventSource.onmessage = (event) => {
        console.log('SSE Message:', event.data);
    };
    
    eventSource.onerror = (error) => {
        console.error('SSE Error:', error);
    };

    // 3. Wait a moment, then send search request
    setTimeout(async () => {
        console.log('\nSending search request...');
        
        const searchRequest = {
            jsonrpc: "2.0",
            method: "tools/call",
            params: {
                name: "brave_web_search",
                arguments: {
                    query: "Claude 3 AI assistant latest features",
                    count: 3
                }
            },
            id: 1
        };
        
        try {
            const response = await fetch('http://localhost:8080/messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchRequest)
            });
            
            console.log('POST Response Status:', response.status);
            const responseText = await response.text();
            if (responseText) {
                console.log('POST Response:', responseText);
            }
        } catch (error) {
            console.error('Search request failed:', error);
        }
    }, 1000);
    
    // Keep running for 10 seconds to see results
    setTimeout(() => {
        console.log('\nClosing connection...');
        eventSource.close();
        process.exit(0);
    }, 10000);
}

testBraveSearchSSE();
