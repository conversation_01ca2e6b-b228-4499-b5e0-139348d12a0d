{"name": "brave-search-mcp-sse", "version": "1.0.10", "description": "An MCP server implementation that integrates the Brave Search API, providing both web and local search capabilities.", "type": "module", "main": "dist/index.js", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@types/express": "^5.0.1", "cors": "^2.8.5", "express": "^4.21.2", "prom-client": "^15.1.3", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/node": "^22.13.11", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.2"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --watch src --ext ts --exec \"node --loader ts-node/esm --no-warnings src/index.ts\"", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC"}