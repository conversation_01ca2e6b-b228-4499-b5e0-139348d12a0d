namespace: "brave-search"

replicaCount: 1

image:
  repository: shoofio/brave-search-mcp-sse
  tag: "1.0.10"
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 8080

serviceMonitor:
  enabled: true
  interval: 30s
  scrapeTimeout: 10s
  additionalLabels:
    release: prometheus

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: example.com
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

# Brave Search API configuration
braveSearch:
  # The API key for Brave Search. You can get one at https://api.search.brave.com/app/keys
  # Can be provided either here or as a Kubernetes secret
  apiKey: ""  # Don't put actual API key here in source control
  # If you want to use an existing secret instead of the apiKey value above
  existingSecret: "brave-search-secret"
  existingSecretKey: "api-key"

# Model Context Protocol (MCP) configuration
# These values are used to identify this server to LLM clients
mcp:
  # The name that identifies this MCP server to clients
  name: "brave-search-sse"
  # The version of this MCP server implementation
  version: "1.0.2"