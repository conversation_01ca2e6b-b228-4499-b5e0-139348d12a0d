// Test Brave Search API directly
import fetch from 'node-fetch';

async function testBraveAPI() {
    console.log('🔍 Testing Brave Search API...\n');
    
    const apiKey = 'BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ';
    const query = 'Model Context Protocol MCP';
    
    try {
        const url = new URL('https://api.search.brave.com/res/v1/web/search');
        url.searchParams.set('q', query);
        url.searchParams.set('count', '3');
        
        console.log('🌐 Making request to Brave API...');
        console.log('Query:', query);
        
        const response = await fetch(url, {
            headers: {
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip',
                'X-Subscription-Token': apiKey
            }
        });
        
        console.log('📊 Response Status:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ API Error:', response.status, response.statusText);
            console.error('Error details:', errorText.substring(0, 200));
            return;
        }
        
        const data = await response.json();
        console.log('✅ API Response received!');
        
        if (data.web && data.web.results) {
            console.log(`📝 Found ${data.web.results.length} results:`);
            data.web.results.forEach((result, index) => {
                console.log(`\n${index + 1}. ${result.title}`);
                console.log(`   ${result.description}`);
                console.log(`   ${result.url}`);
            });
        } else {
            console.log('⚠️ No web results found in response');
        }
        
        console.log('\n🎉 Brave API test successful!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testBraveAPI();
