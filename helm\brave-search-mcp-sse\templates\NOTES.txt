Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

CONFIGURATION NOTES:
------------------
1. Brave Search API Key:
   {{- if .Values.braveSearch.apiKey }}
   - Using API key provided in values
   {{- else if .Values.braveSearch.existingSecret }}
   - Using existing secret: {{ .Values.braveSearch.existingSecret }}
   - Secret key: {{ .Values.braveSearch.existingSecretKey }}
   {{- else }}
   WARNING: No API key or existing secret specified. The server will not function without a valid Brave Search API key.
   {{- end }}

DEPLOYMENT STATUS:
----------------
To verify the deployment status, run:
  kubectl get pods -l "app.kubernetes.io/instance={{ .Release.Name }}"

To get the service URL, run:
  kubectl get svc {{ include "brave-search-mcp-sse.fullname" . }} --output jsonpath='{.status.loadBalancer.ingress[0].ip}'

To get the logs, run:
  kubectl logs -l "app.kubernetes.io/instance={{ .Release.Name }}"

{{- if .Values.ingress.enabled }}
The application is accessible via the Ingress at:
{{- range .Values.ingress.hosts }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ .host }}{{ .paths | first | pluck "path" | first }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
To access the service from outside the cluster, use:
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "brave-search-mcp-sse.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
To access the service, wait for the load balancer to be provisioned, then run:
  kubectl get svc {{ include "brave-search-mcp-sse.fullname" . }} --output jsonpath='{.status.loadBalancer.ingress[0].ip}'
{{- else if contains "ClusterIP" .Values.service.type }}
To access the service from within the cluster, use:
  kubectl port-forward svc/{{ include "brave-search-mcp-sse.fullname" . }} {{ .Values.service.port }}:{{ .Values.service.port }}
{{- end }}

CONFIGURATION HELP:
-----------------
To use an existing secret:
1. Make sure your secret exists in the namespace
2. The secret should have your Brave API key stored under the key "{{ .Values.braveSearch.existingSecretKey }}"
3. The chart is configured to use secret named "{{ .Values.braveSearch.existingSecret }}"

To provide the API key directly in values:
  helm upgrade {{ .Release.Name }} . --set braveSearch.apiKey=your-api-key 