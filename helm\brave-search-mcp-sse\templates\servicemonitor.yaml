{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "brave-search-mcp-sse.fullname" . }}
  {{- if .Values.namespace }}
  namespace: {{ .Values.namespace }}
  {{- end }}
  labels:
    {{- include "brave-search-mcp-sse.labels" . | nindent 4 }}
    {{- with .Values.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  endpoints:
    - port: http
      path: /metrics
      interval: {{ .Values.serviceMonitor.interval }}
      scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
  selector:
    matchLabels:
      {{- include "brave-search-mcp-sse.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
      - {{ .Values.namespace | default .Release.Namespace }}
{{- end }} 