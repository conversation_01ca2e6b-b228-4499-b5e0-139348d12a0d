# Brave Search MCP Server für Letta AI

## Schnellstart

1. **Server starten:**
   ```cmd
   start-for-letta.bat
   ```

2. **In Letta AI konfigurieren:**
   - Server Name: `Brave Search MCP`
   - Server URL: `http://localhost:8080/sse`

## Verfügbare Tools

### 1. brave_web_search
- **Beschreibung:** Führt eine Websuche mit der Brave Search API durch
- **Parameter:**
  - `query` (erforderlich): Suchbegriff (max 400 Zeichen, 50 Wörter)
  - `count` (optional): Anzahl der Ergebnisse (1-20, Standard: 10)
  - `offset` (optional): Pagination Offset (max 9, Standard: 0)

### 2. brave_local_search
- **Beschreibung:** Sucht nach lokalen Unternehmen und Orten
- **Parameter:**
  - `query` (erforderlich): Lokale Suchanfrage (z.B. "Pizza in Berlin")
  - `count` (optional): An<PERSON><PERSON> der Ergebnisse (1-20, Standard: 5)

## Endpoints

- **Health Check:** http://localhost:8080/health
- **SSE Endpoint:** http://localhost:8080/sse
- **Metrics:** http://localhost:8080/metrics

## Konfiguration

Die folgenden Umgebungsvariablen sind bereits konfiguriert:
- `BRAVE_API_KEY`: BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ
- `PORT`: 8080
- `LOG_LEVEL`: info

## Troubleshooting

1. **Port bereits in Verwendung:**
   - Ändere den Port in `start-for-letta.bat`
   - Aktualisiere die URL in Letta AI entsprechend

2. **API-Fehler:**
   - Überprüfe den API-Key in `start-for-letta.bat`
   - Stelle sicher, dass der Brave API-Key gültig ist

3. **Verbindungsprobleme:**
   - Stelle sicher, dass der Server läuft
   - Überprüfe die Firewall-Einstellungen
   - Teste mit: `curl http://localhost:8080/health`

## Features

- ✅ Server-Sent Events (SSE) Transport
- ✅ CORS-Unterstützung für Letta AI
- ✅ Automatische Fehlerbehandlung
- ✅ Rate Limiting
- ✅ Prometheus Metriken
- ✅ Strukturierte Logs
- ✅ Health Check Endpoint
