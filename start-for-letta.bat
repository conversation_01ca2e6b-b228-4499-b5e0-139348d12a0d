@echo off
echo ====================================
echo Starting Brave Search MCP SSE Server for Letta AI
echo ====================================

:: Setze den API Key
set BRAVE_API_KEY=BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ

:: Port für Letta AI (Standard ist 8080)
set PORT=8080

:: Log Level
set LOG_LEVEL=info

:: Public URL für Letta AI
set PUBLIC_URL=http://localhost:8080

echo.
echo Configuration for Letta AI:
echo - Port: %PORT%
echo - Log Level: %LOG_LEVEL%
echo - API Key: [CONFIGURED]
echo - Public URL: %PUBLIC_URL%
echo.

:: Prüfe ob dist Ordner existiert
if not exist "dist" (
    echo Building TypeScript files...
    call npm run build
    if errorlevel 1 (
        echo Build failed!
        pause
        exit /b 1
    )
)

echo Starting server for Letta AI...
echo.
echo ====================================
echo LETTA AI CONFIGURATION:
echo ====================================
echo Server Name: Brave Search MCP
echo Server URL: %PUBLIC_URL%/sse
echo.
echo Copy this URL into Letta AI:
echo %PUBLIC_URL%/sse
echo ====================================
echo.

:: Starte den Server
node dist/index.js

:: Falls der Server abstürzt
echo.
echo Server stopped!
pause
