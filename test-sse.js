import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { setupSSETransport } from './dist/transport/sseTransport.js';

console.log('Starting SSE test...');

try {
  const server = new Server(
    { name: "test", version: "1.0.0" },
    { capabilities: { tools: {} } }
  );
  console.log('Server created successfully');
  
  const httpServer = setupSSETransport(server);
  console.log('SSE transport setup successful');
  
  httpServer.on('error', (error) => {
    console.error('HTTP Server error:', error);
  });
  
  console.log('Test completed successfully');
  
  // Close the server after a short delay
  setTimeout(() => {
    httpServer.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  }, 2000);
  
} catch (error) {
  console.error('Test failed:', error);
  process.exit(1);
}
