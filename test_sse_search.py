import requests
import json
import sseclient
import threading
import time

def test_brave_search_sse():
    """Test Brave Search MCP SSE Server"""
    
    print("Testing Brave Search MCP SSE Server...")
    print("-" * 50)
    
    # First, check health
    try:
        health = requests.get("http://localhost:8080/health")
        print(f"Health Check: {health.json()}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # Create SSE connection
    print("\nEstablishing SSE connection...")
    
    try:
        # Open SSE stream
        response = requests.get("http://localhost:8080/sse", stream=True)
        client = sseclient.SSEClient(response)
        
        print("SSE connection established!")
        
        # Listen for events in a separate thread
        def listen_events():
            for event in client.events():
                if event.data:
                    print(f"\nSSE Event: {event.event}")
                    print(f"Data: {event.data}")
        
        listener = threading.Thread(target=listen_events)
        listener.daemon = True
        listener.start()
        
        # Wait a moment for connection to stabilize
        time.sleep(1)
        
        # Now send a search request via POST
        search_request = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": "brave_web_search",
                "arguments": {
                    "query": "Model Context Protocol MCP latest developments 2025",
                    "count": 3
                }
            },
            "id": "test-1"
        }
        
        print(f"\nSending search request: {search_request['params']['arguments']['query']}")
        
        post_response = requests.post(
            "http://localhost:8080/messages",
            json=search_request,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"POST Response Status: {post_response.status_code}")
        if post_response.text:
            print(f"POST Response: {post_response.text}")
        
        # Wait for SSE events
        print("\nWaiting for search results via SSE...")
        time.sleep(5)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_brave_search_sse()
