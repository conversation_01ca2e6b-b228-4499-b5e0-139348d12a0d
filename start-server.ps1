# Brave Search MCP SSE Server - PowerShell Start Script

Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Starting Brave Search MCP SSE Server" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Setze Umgebungsvariablen
$env:BRAVE_API_KEY = "BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ"
$env:PORT = "8080"
$env:LOG_LEVEL = "info"

# Optional: Public URL
# $env:PUBLIC_URL = "http://localhost:8080"

Write-Host ""
Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "- Port: $env:PORT"
Write-Host "- Log Level: $env:LOG_LEVEL"
Write-Host "- API Key: [CONFIGURED]"
Write-Host ""

# Prüfe ob dist Ordner existiert
if (-not (Test-Path "dist")) {
    Write-Host "Building TypeScript files..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "Starting server..." -ForegroundColor Green
Write-Host ""

# Starte den Server
node dist/index.js

# Falls der Server stoppt
Write-Host ""
Write-Host "Server stopped!" -ForegroundColor Red
Read-Host "Press Enter to exit"
