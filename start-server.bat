@echo off
echo ====================================
echo Starting Brave Search MCP SSE Server
echo ====================================

:: Setze den API Key (ersetze YOUR_API_KEY_HERE mit deinem echten Key)
set BRAVE_API_KEY=BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ

:: Optional: Port konfigurieren (Standard ist 8080)
set PORT=8080

:: Optional: Log Level setzen
set LOG_LEVEL=info

:: Optional: Public URL wenn du es extern verfügbar machen willst
:: set PUBLIC_URL=http://localhost:8080

echo.
echo Configuration:
echo - Port: %PORT%
echo - Log Level: %LOG_LEVEL%
echo - API Key: [CONFIGURED]
echo.

:: Prüfe ob dist Ordner existiert
if not exist "dist" (
    echo Building TypeScript files...
    call npm run build
    if errorlevel 1 (
        echo Build failed!
        pause
        exit /b 1
    )
)

echo Starting server...
echo.

:: Starte den Server
node dist/index.js

:: Falls der Server abstürzt
echo.
echo Server stopped!
pause
