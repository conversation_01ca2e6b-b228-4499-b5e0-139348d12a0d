# 🎯 BRAVE SEARCH MCP SERVER - FINALE ANLEITUNG FÜR LETTA AI

## ✅ STATUS: VOLLSTÄNDIG GETESTET UND FUNKTIONSFÄHIG

### 🚀 SCHNELLSTART FÜR LETTA AI

**1. Server starten:**
```powershell
powershell -ExecutionPolicy Bypass -File start-letta.ps1
```

**2. In Letta AI konfigurieren:**
- **Server Name:** `Brave Search MCP`
- **Server URL:** `http://localhost:8080/sse`

### 📋 GETESTETE FUNKTIONEN

✅ **Brave Search API** - Funktioniert perfekt  
✅ **SSE Transport** - Server-Sent Events aktiv  
✅ **CORS Support** - Für Letta AI konfiguriert  
✅ **Health Check** - http://localhost:8080/health  
✅ **Metrics** - http://localhost:8080/metrics  
✅ **Error Handling** - Robuste Fehlerbehandlung  

### 🔧 VERFÜGBARE TOOLS

#### 1. brave_web_search
```json
{
  "name": "brave_web_search",
  "description": "Websuche mit Brave Search API",
  "parameters": {
    "query": "Suchbegriff (erforderlich)",
    "count": "Anzahl Ergebnisse (1-20, Standard: 10)",
    "offset": "Pagination (0-9, Standard: 0)"
  }
}
```

#### 2. brave_local_search
```json
{
  "name": "brave_local_search", 
  "description": "Lokale Suche nach Unternehmen",
  "parameters": {
    "query": "Lokale Suchanfrage (erforderlich)",
    "count": "Anzahl Ergebnisse (1-20, Standard: 5)"
  }
}
```

### 🌐 ENDPOINTS

- **SSE (Letta AI):** `http://localhost:8080/sse`
- **Health Check:** `http://localhost:8080/health`
- **Metrics:** `http://localhost:8080/metrics`

### 🔑 KONFIGURATION

```powershell
$env:BRAVE_API_KEY = "BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ"
$env:PORT = "8080"
$env:LOG_LEVEL = "info"
$env:PUBLIC_URL = "http://localhost:8080"
```

### 🧪 TESTS DURCHGEFÜHRT

1. ✅ **Health Check Test** - Server antwortet korrekt
2. ✅ **SSE Endpoint Test** - Content-Type: text/event-stream
3. ✅ **Metrics Test** - Prometheus Metriken verfügbar
4. ✅ **Brave API Test** - 3 Suchergebnisse erhalten
5. ✅ **MCP Protocol Test** - Tools registriert und verfügbar

### 🎯 FÜR LETTA AI EINFÜGEN:

**Server Name:** `Brave Search MCP`  
**Server URL:** `http://localhost:8080/sse`

### 🔄 NEUSTART BEI PROBLEMEN:

```powershell
# Server stoppen (Ctrl+C)
# Dann neu starten:
powershell -ExecutionPolicy Bypass -File start-letta.ps1
```

### 📊 BEISPIEL-NUTZUNG IN LETTA AI:

Nach der Konfiguration kannst du in Letta AI fragen:
- "Suche nach aktuellen Nachrichten über KI"
- "Finde Restaurants in Berlin"
- "Was ist das Model Context Protocol?"

Der Server wird automatisch die entsprechenden Tools verwenden!
