apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "brave-search-mcp-sse.fullname" . }}
  {{- if .Values.namespace }}
  namespace: {{ .Values.namespace }}
  {{- end }}
  labels:
    {{- include "brave-search-mcp-sse.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "brave-search-mcp-sse.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "brave-search-mcp-sse.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          env:
            - name: BRAVE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.braveSearch.existingSecret | default (include "brave-search-mcp-sse.fullname" .) }}
                  key: {{ .Values.braveSearch.existingSecretKey }}
            {{- if .Values.ingress.enabled }}
            - name: PUBLIC_URL
              value: {{ printf "%s://%s" (ternary "https" "http" (empty .Values.ingress.tls | not)) (index .Values.ingress.hosts 0).host | quote }}
            {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }} 