// Kompletter Test des MCP Servers
import fetch from 'node-fetch';

async function completeTest() {
    console.log('🎯 KOMPLETTER MCP SERVER TEST\n');
    console.log('====================================');
    
    const baseUrl = 'http://localhost:8080';
    let allTestsPassed = true;
    
    // Test 1: Health Check
    console.log('\n1️⃣ HEALTH CHECK TEST');
    try {
        const response = await fetch(`${baseUrl}/health`);
        const data = await response.json();
        if (response.status === 200 && data.status === 'ok') {
            console.log('✅ Health Check: PASSED');
        } else {
            console.log('❌ Health Check: FAILED');
            allTestsPassed = false;
        }
    } catch (error) {
        console.log('❌ Health Check: ERROR -', error.message);
        allTestsPassed = false;
    }
    
    // Test 2: SSE Endpoint
    console.log('\n2️⃣ SSE ENDPOINT TEST');
    try {
        const response = await fetch(`${baseUrl}/sse`);
        const contentType = response.headers.get('content-type');
        if (response.status === 200 && contentType === 'text/event-stream') {
            console.log('✅ SSE Endpoint: PASSED');
            console.log('   - Status: 200');
            console.log('   - Content-Type: text/event-stream');
        } else {
            console.log('❌ SSE Endpoint: FAILED');
            allTestsPassed = false;
        }
    } catch (error) {
        console.log('❌ SSE Endpoint: ERROR -', error.message);
        allTestsPassed = false;
    }
    
    // Test 3: Metrics
    console.log('\n3️⃣ METRICS TEST');
    try {
        const response = await fetch(`${baseUrl}/metrics`);
        if (response.status === 200) {
            console.log('✅ Metrics: PASSED');
        } else {
            console.log('❌ Metrics: FAILED');
            allTestsPassed = false;
        }
    } catch (error) {
        console.log('❌ Metrics: ERROR -', error.message);
        allTestsPassed = false;
    }
    
    // Test 4: Brave API (direkt)
    console.log('\n4️⃣ BRAVE API TEST');
    try {
        const url = new URL('https://api.search.brave.com/res/v1/web/search');
        url.searchParams.set('q', 'test');
        url.searchParams.set('count', '1');
        
        const response = await fetch(url, {
            headers: {
                'Accept': 'application/json',
                'X-Subscription-Token': 'BSAP4gQE0jRLz-dC_fy-YzsDSTA3FeJ'
            }
        });
        
        if (response.status === 200) {
            console.log('✅ Brave API: PASSED');
        } else {
            console.log('❌ Brave API: FAILED - Status:', response.status);
            allTestsPassed = false;
        }
    } catch (error) {
        console.log('❌ Brave API: ERROR -', error.message);
        allTestsPassed = false;
    }
    
    // Finale Bewertung
    console.log('\n====================================');
    if (allTestsPassed) {
        console.log('🎉 ALLE TESTS BESTANDEN!');
        console.log('\n📋 LETTA AI KONFIGURATION:');
        console.log('   Server Name: Brave Search MCP');
        console.log('   Server URL: http://localhost:8080/sse');
        console.log('\n🚀 Der Server ist bereit für Letta AI!');
    } else {
        console.log('❌ EINIGE TESTS FEHLGESCHLAGEN!');
        console.log('   Bitte überprüfe die Konfiguration.');
    }
    console.log('====================================');
}

completeTest();
